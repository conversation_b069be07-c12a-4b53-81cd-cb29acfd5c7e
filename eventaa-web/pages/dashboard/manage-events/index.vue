<template>
  <div class="w-full h-full bg-gray-50 min-h-screen">
    <!-- Header with Breadcrumb -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumb -->
        <div class="flex items-center py-3 text-sm">
          <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
              <li>
                <div class="flex items-center">
                  <a href="/dashboard" class="text-gray-500 hover:text-gray-700">
                    Workstream Delta
                  </a>
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <Icon icon="heroicons:chevron-right" class="w-4 h-4 text-gray-400 mx-2" />
                  <span class="text-gray-900 font-medium">Open Items</span>
                </div>
              </li>
            </ol>
          </nav>
        </div>

        <!-- Main Header -->
        <div class="flex items-center justify-between pb-6">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Open Items</h1>
          </div>
          <div class="flex items-center space-x-3">
            <!-- Status Filter Tabs -->
            <div class="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                v-for="status in statusTabs"
                :key="status.value"
                @click="setActiveStatusTab(status.value)"
                class="px-3 py-1.5 text-sm font-medium rounded-md transition-colors"
                :class="activeStatusTab === status.value
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'"
              >
                <Icon :icon="status.icon" class="w-4 h-4 mr-1.5" />
                {{ status.name }}
              </button>
            </div>

            <button
              @click="exportEvents"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors rounded-md"
            >
              <Icon icon="heroicons:arrow-down-tray" class="w-4 h-4 mr-2" />
              Export
            </button>
            <CorePrimaryButton
              @click="$router.push('/dashboard/manage-events/create')"
              text="Create event"
              start-icon="heroicons:plus"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Filters Section -->
      <div class="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
              <h2 class="text-lg font-semibold text-gray-900">Filters</h2>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">Delta</span>
                <Icon icon="heroicons:chevron-down" class="w-4 h-4 text-gray-400" />
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <button
                v-if="hasActiveFilters"
                @click="clearFilters"
                class="text-sm text-red-600 hover:text-red-500 font-medium flex items-center space-x-1"
              >
                <Icon icon="heroicons:x-mark" class="w-4 h-4" />
                <span>Clear Filters</span>
              </button>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">Omega</span>
                <Icon icon="heroicons:chevron-down" class="w-4 h-4 text-gray-400" />
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">Nexus</span>
                <Icon icon="heroicons:chevron-down" class="w-4 h-4 text-gray-400" />
              </div>
            </div>
          </div>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Search Bar -->
          <div class="lg:col-span-2">
            <label
              for="search"
              class="block text-sm font-medium text-gray-700 mb-2"
              >Search Events</label
            >
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <Icon
                  icon="material-symbols:search"
                  class="h-5 w-5 text-gray-400"
                />
              </div>
              <input
                v-model="searchQuery"
                type="text"
                id="search"
                placeholder="Search by title, description, or location..."
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-red-500 focus:border-red-500 text-sm"
              />
            </div>
          </div>

          <!-- Status Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Status</label
            >
            <Listbox
              v-model="selectedStatus"
              @update:model-value="onStatusChange"
            >
              <div class="relative">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 cursor-default focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 text-sm"
                >
                  <span class="block truncate">{{ selectedStatus.name }}</span>
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <Icon
                      icon="heroicons:chevron-up-down"
                      class="w-5 h-5 text-gray-400"
                    />
                  </span>
                </ListboxButton>
                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto bg-white shadow-lg max-h-60 border border-gray-300 focus:outline-none text-sm"
                  >
                    <ListboxOption
                      v-slot="{ active, selected }"
                      v-for="status in statusOptions"
                      :key="status.value"
                      :value="status"
                      as="template"
                    >
                      <li
                        :class="[
                          active ? 'text-white bg-red-600' : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-10 pr-4',
                        ]"
                      >
                        <span
                          :class="[
                            selected ? 'font-medium' : 'font-normal',
                            'block truncate',
                          ]"
                          >{{ status.name }}</span
                        >
                        <span
                          v-if="selected"
                          class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600"
                        >
                          <Icon
                            icon="heroicons:check"
                            class="w-5 h-5"
                            :class="[active ? 'text-white' : 'text-red-600']"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>

          <!-- Date Range Filter -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Date Range</label
            >
            <Listbox
              v-model="selectedDateRange"
              @update:model-value="onDateRangeChange"
            >
              <div class="relative">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 cursor-default focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 text-sm"
                >
                  <span class="block truncate">{{
                    selectedDateRange.name
                  }}</span>
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <Icon
                      icon="heroicons:chevron-up-down"
                      class="w-5 h-5 text-gray-400"
                    />
                  </span>
                </ListboxButton>
                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto bg-white shadow-lg max-h-60 border border-gray-300 focus:outline-none text-sm"
                  >
                    <ListboxOption
                      v-slot="{ active, selected }"
                      v-for="dateRange in dateRangeOptions"
                      :key="dateRange.value"
                      :value="dateRange"
                      as="template"
                    >
                      <li
                        :class="[
                          active ? 'text-white bg-red-600' : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-10 pr-4',
                        ]"
                      >
                        <span
                          :class="[
                            selected ? 'font-medium' : 'font-normal',
                            'block truncate',
                          ]"
                          >{{ dateRange.name }}</span
                        >
                        <span
                          v-if="selected"
                          class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600"
                        >
                          <Icon
                            icon="heroicons:check"
                            class="w-5 h-5"
                            :class="[active ? 'text-white' : 'text-red-600']"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>

          <!-- Items Per Page -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2"
              >Items Per Page</label
            >
            <Listbox
              v-model="selectedItemsPerPage"
              @update:model-value="onItemsPerPageChange"
            >
              <div class="relative">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 cursor-default focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 text-sm"
                >
                  <span class="block truncate">{{
                    selectedItemsPerPage.name
                  }}</span>
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <Icon
                      icon="heroicons:chevron-up-down"
                      class="w-5 h-5 text-gray-400"
                    />
                  </span>
                </ListboxButton>
                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto bg-white shadow-lg max-h-60 border border-gray-300 focus:outline-none text-sm"
                  >
                    <ListboxOption
                      v-slot="{ active, selected }"
                      v-for="option in itemsPerPageOptions"
                      :key="option.value"
                      :value="option"
                      as="template"
                    >
                      <li
                        :class="[
                          active ? 'text-white bg-red-600' : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-10 pr-4',
                        ]"
                      >
                        <span
                          :class="[
                            selected ? 'font-medium' : 'font-normal',
                            'block truncate',
                          ]"
                          >{{ option.name }}</span
                        >
                        <span
                          v-if="selected"
                          class="absolute inset-y-0 left-0 flex items-center pl-3 text-red-600"
                        >
                          <Icon
                            icon="heroicons:check"
                            class="w-5 h-5"
                            :class="[active ? 'text-white' : 'text-red-600']"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>
        </div>
      </div>

      <div
        class="mt-6 bg-white shadow-sm border border-gray-200 overflow-hidden"
      >
        <EventsDashboardDatatable
          :headers="headers"
          :events="filteredEvents"
          :totalItems="filteredEvents.length"
          :loading="loading"
          :server-options="serverOptions"
          @update:events="fetchUserEvents"
          @update:options="handleOptionsUpdate"
        />
      </div>
    </div>

    <!-- Event View Dialog -->
    <TransitionRoot appear :show="viewDialogOpen" as="template">
      <Dialog as="div" @close="closeViewDialog" class="relative z-10">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black/25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div
            class="flex min-h-full items-center justify-center p-4 text-center"
          >
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel
                class="w-full max-w-3xl transform overflow-hidden bg-white text-left align-middle shadow-xl transition-all rounded-lg"
              >
                <div class="px-6 py-4 border-b">
                  <DialogTitle
                    as="h3"
                    class="text-lg font-medium leading-6 text-gray-900"
                  >
                    {{ selectedEvent?.title }}
                  </DialogTitle>
                </div>

                <div class="p-6">
                  <div class="space-y-4">
                    <div>
                      <h4 class="text-sm font-medium text-gray-700">
                        Description
                      </h4>
                      <div
                        class="mt-1 text-sm text-gray-500"
                        v-html="selectedEvent?.description"
                      ></div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 class="text-sm font-medium text-gray-700">
                          Location
                        </h4>
                        <p class="mt-1 text-sm text-gray-500">
                          {{ selectedEvent?.location }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700">
                          Status
                        </h4>
                        <p class="mt-1 text-sm text-gray-500">
                          {{ selectedEvent?.status }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700">
                          Start Date
                        </h4>
                        <p class="mt-1 text-sm text-gray-500">
                          {{ selectedEvent?.start }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700">
                          End Date
                        </h4>
                        <p class="mt-1 text-sm text-gray-500">
                          {{ selectedEvent?.end }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700">
                          Published At
                        </h4>
                        <p class="mt-1 text-sm text-gray-500">
                          {{ selectedEvent?.published_at }}
                        </p>
                      </div>

                      <div>
                        <h4 class="text-sm font-medium text-gray-700">
                          Date Created
                        </h4>
                        <p class="mt-1 text-sm text-gray-500">
                          {{ selectedEvent?.created_at }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="px-6 py-4 bg-gray-50 flex justify-end">
                  <CorePrimaryButton @click="closeViewDialog" text="Close" />
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionRoot,
  TransitionChild,
} from "@headlessui/vue";

definePageMeta({
  layout: "dashboard",
});
useHead({
  title: "Event Management - EventaHub Malawi",
});

import type { Header } from "vue3-easy-data-table";
import type { EventItem } from "@/types";
import type { FilterOption, ServerOptions } from "@/types/datatable";
import dayjs from "dayjs";

const httpClient = useHttpClient();
const { $toast }: any = useNuxtApp();
const headers: Header[] = [
  { text: "TITLE", value: "title", sortable: true },
  { text: "DESCRIPTION", value: "description", sortable: true },
  { text: "LOCATION", value: "location", sortable: true },
  { text: "START DATE", value: "start", sortable: true },
  { text: "END DATE", value: "end", sortable: true },
  { text: "STATUS", value: "status", sortable: true },
  { text: "PUBLISHED AT", value: "published_at", sortable: true },
  { text: "DATE CREATED", value: "created_at", sortable: true },
  { text: "ACTIONS", value: "actions" },
];

const loading = ref<boolean>(false);
const events = ref<EventItem[]>([]);
const totalItems = ref<number>(0);
const searchQuery = ref<string>("");
const statusFilter = ref<string | number>("");
const dateRangeFilter = ref<string | number>("");
const itemsPerPage = ref<number>(25);
const currentPage = ref<number>(1);
const viewDialogOpen = ref<boolean>(false);
// Extended event item with display fields
interface DisplayEventItem extends EventItem {
  status?: string;
}

const selectedEvent = ref<DisplayEventItem | null>(null);

// Server options for vue3-easy-data-table
const serverOptions = ref<ServerOptions>({
  page: 1,
  rowsPerPage: 25,
});

// Headless UI Select Options
const statusOptions: FilterOption[] = [
  { name: "All Status", value: "" },
  { name: "Published", value: "published" },
  { name: "Draft", value: "draft" },
  { name: "Cancelled", value: "cancelled" },
];

const dateRangeOptions: FilterOption[] = [
  { name: "All Dates", value: "" },
  { name: "Today", value: "today" },
  { name: "This Week", value: "week" },
  { name: "This Month", value: "month" },
  { name: "Upcoming", value: "upcoming" },
  { name: "Past Events", value: "past" },
];

const itemsPerPageOptions: FilterOption[] = [
  { name: "10", value: 10 },
  { name: "25", value: 25 },
  { name: "50", value: 50 },
  { name: "100", value: 100 },
];

const selectedStatus = ref<FilterOption>(statusOptions[0]);
const selectedDateRange = ref<FilterOption>(dateRangeOptions[0]);
const selectedItemsPerPage = ref<FilterOption>(itemsPerPageOptions[1]);

// Status tabs for header
const statusTabs = [
  { name: "Requested", value: "requested", icon: "heroicons:clock" },
  { name: "Approved", value: "approved", icon: "heroicons:check-circle" },
  { name: "Processing", value: "processing", icon: "heroicons:cog-6-tooth" },
  { name: "Dispatched", value: "dispatched", icon: "heroicons:truck" },
];

const activeStatusTab = ref<string>("requested");

// Status tab handler
const setActiveStatusTab = (status: string): void => {
  activeStatusTab.value = status;
  // You can add filtering logic here based on the selected status tab
};

const formattedEvents = computed(() => {
  return events.value.map((event) => {
    return {
      ...event,
      start: dayjs(event.start).format("DD/MM/YYYY HH:mm"),
      end: dayjs(event.end).format("DD/MM/YYYY HH:mm"),
      created_at: dayjs(event.created_at).format("DD/MM/YYYY"),
      published_at: event.published_at
        ? dayjs(event.published_at).format("DD/MM/YYYY")
        : "Not Published",
      status: getEventStatus(event),
    };
  });
});

const filteredEvents = computed(() => {
  let filtered = [...formattedEvents.value];
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(
      (event) =>
        event.title?.toLowerCase().includes(query) ||
        event.description?.toLowerCase().includes(query) ||
        event.location?.toLowerCase().includes(query)
    );
  }

  if (statusFilter.value) {
    const statusVal = String(statusFilter.value).toLowerCase();
    filtered = filtered.filter(
      (event) => event.status?.toLowerCase() === statusVal
    );
  }

  if (dateRangeFilter.value) {
    const now = dayjs();
    filtered = filtered.filter((event) => {
      const eventStart = dayjs(event.start, "DD/MM/YYYY HH:mm");

      switch (dateRangeFilter.value) {
        case "today":
          return eventStart.isSame(now, "day");
        case "week":
          return (
            eventStart.isAfter(now.startOf("week")) &&
            eventStart.isBefore(now.endOf("week"))
          );
        case "month":
          return (
            eventStart.isAfter(now.startOf("month")) &&
            eventStart.isBefore(now.endOf("month"))
          );
        case "upcoming":
          return eventStart.isAfter(now);
        case "past":
          return eventStart.isBefore(now);
        default:
          return true;
      }
    });
  }

  return filtered;
});

const hasActiveFilters = computed(() => {
  return (
    searchQuery.value.trim() !== "" ||
    statusFilter.value !== "" ||
    dateRangeFilter.value !== ""
  );
});

const getEventStatus = (event: EventItem): string => {
  const now = dayjs();
  const eventStart = dayjs(event.start);
  const eventEnd = dayjs(event.end);

  if (!event.published_at) return "Draft";
  if (eventEnd.isBefore(now)) return "Completed";
  if (eventStart.isAfter(now)) return "Upcoming";
  if (eventStart.isBefore(now) && eventEnd.isAfter(now)) return "Live";
  return "Published";
};

const fetchUserEvents = async (): Promise<void> => {
  loading.value = true;
  try {
    const response = await httpClient.get<any>(ENDPOINTS.EVENTS.USER);

    if (response) {
      events.value = response.events.data;
      totalItems.value = response.events.total;
    }
  } catch (error) {
    $toast.error("An error occurred while fetching events.");
  } finally {
    loading.value = false;
  }
};

const closeViewDialog = (): void => {
  viewDialogOpen.value = false;
  selectedEvent.value = null;
};

const exportEvents = async (): Promise<void> => {
  loading.value = true;
  try {
    // Define the structure of our export data with explicit types
    interface ExportRow {
      Title: string;
      Description: string;
      Location: string;
      "Start Date": string;
      "End Date": string;
      Status: string;
      "Published At": string;
      "Date Created": string;
      Attendees: number;
      Likes: number;
      Category: string;
    }

    const dataToExport: ExportRow[] = formattedEvents.value.map((event) => ({
      Title: event.title,
      Description: event.description?.replace(/<[^>]*>/g, "") || "",
      Location: event.location,
      "Start Date": event.start,
      "End Date": event.end,
      Status: event.status || "",
      "Published At": event.published_at,
      "Date Created": event.created_at,
      Attendees: event.attendees_count || 0,
      Likes: event.likes_count || 0,
      Category: event.category?.name || "Uncategorized",
    }));

    if (dataToExport.length === 0) {
      $toast.info("No events to export.");
      loading.value = false;
      return;
    }

    // Convert to CSV
    const headers = Object.keys(dataToExport[0]).join(",");
    const csv = dataToExport.map((row) =>
      Object.values(row)
        .map((value) => `"${String(value).replace(/"/g, '""')}"`)
        .join(",")
    );

    const csvContent = [headers, ...csv].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `events-export-${dayjs().format("YYYY-MM-DD")}.csv`
    );
    document.body.appendChild(link); // Required for Firefox
    link.click();
    document.body.removeChild(link); // Clean up
    window.URL.revokeObjectURL(url); // Free memory

    $toast.success("Events exported successfully!");
  } catch (error) {
    console.error("Export error:", error);
    $toast.error("An error occurred while exporting events.");
  } finally {
    loading.value = false;
  }
};

const clearFilters = (): void => {
  searchQuery.value = "";
  statusFilter.value = "";
  dateRangeFilter.value = "";
  currentPage.value = 1;
  serverOptions.value.page = 1;

  // Reset Headless UI selections
  selectedStatus.value = statusOptions[0];
  selectedDateRange.value = dateRangeOptions[0];
};

const handleOptionsUpdate = (options: ServerOptions): void => {
  serverOptions.value = options;
  currentPage.value = options.page;
  itemsPerPage.value = options.rowsPerPage;
};

// Headless UI event handlers
const onStatusChange = (status: FilterOption): void => {
  statusFilter.value = status.value;
  currentPage.value = 1;
  serverOptions.value.page = 1;
};

const onDateRangeChange = (dateRange: FilterOption): void => {
  dateRangeFilter.value = dateRange.value;
  currentPage.value = 1;
  serverOptions.value.page = 1;
};

const onItemsPerPageChange = (itemsPerPageOption: FilterOption): void => {
  itemsPerPage.value = Number(itemsPerPageOption.value);
  serverOptions.value.rowsPerPage = Number(itemsPerPageOption.value);
  currentPage.value = 1;
  serverOptions.value.page = 1;
};

// Watchers
watch([searchQuery, statusFilter, dateRangeFilter], () => {
  currentPage.value = 1; // Reset to first page when filters change
  serverOptions.value.page = 1;
});

watch(itemsPerPage, (newValue) => {
  serverOptions.value.rowsPerPage = newValue;
  currentPage.value = 1;
  serverOptions.value.page = 1;
});

onMounted(() => {
  fetchUserEvents();
});
</script>

<style lang="css" scoped></style>
