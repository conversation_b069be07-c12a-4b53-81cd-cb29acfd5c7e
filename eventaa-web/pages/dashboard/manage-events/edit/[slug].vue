<template>
    <CoreOverlay :loading="loading" text="Updating event..." />
    <div class="flex flex-col min-h-screen">
        <div class="w-full bg-pattern backdrop-blur-sm border-b border-gray-200">
            <div class="px-4 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <button class="text-gray-600 hover:text-gray-800">
                        <Icon icon="ion:arrow-back-sharp" class="w-6 h-6" />
                    </button>
                    <h1 class="text-2xl font-semibold">Create Event</h1>
                    <span class="bg-gray-300 border font-thin rounded-full px-2 py-1 text-sm">Draft</span>
                </div>
            </div>

            <div class="px-4 text-sm text-gray-500">
                <nav class="flex items-center space-x-2">
                    <span class="font-thin">Dashboard</span>
                    <span class="font-thin">/</span>
                    <span class="font-thin">Manage Events</span>
                    <span class="font-thin">/</span>
                    <span class="text-gray-900">Create Event</span>
                </nav>
            </div>

            <div class="px-4 py-4">
                <nav class="flex items-center justify-between">
                    <div class="flex space-x-8">
                        <button v-for="(step, index) in steps" :key="index" :class="[
                            'flex items-center space-x-2 rounded py-1.5 px-2',
                            currentStep === index + 1
                                ? 'text-red-600 font-medium'
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                        ]">
                            <span class="w-6 h-6 flex items-center justify-center rounded-full" :class="[
                                step.completed ? 'text-white bg-red-500' : 'bg-gray-100',
                                currentStep === index + 1 ? 'bg-red-200' : ''
                            ]">
                                <span v-if="step.completed">✓</span>
                                <span v-else>{{ index + 1 }}</span>
                            </span>
                            <span>{{ step.name }}</span>
                        </button>
                    </div>
                    <EventsCreateProgress :steps="steps" />
                </nav>
            </div>
        </div>
        <div>
            <FormKit v-if="currentStep == 1" id="createEventForm" @submit="onFormSubmit" type="form"
                submit-label="Update" :actions="false" #default="{}">
                <div class="flex-grow">
                    <div class="bg-white px-4 py-2">
                        <p class="font-light">Please fill in the required fields to create an event.</p>
                    </div>

                    <div class="w-full relative bg-white sm:grid sm:grid-cols-7">
                        <div class="col-span-5 order-1">
                            <div class="flex flex-col px-4 py-2 space-y-2">
                                <div class="w-full flex flex-col space-y-3">
                                    <FormKit type="text" v-model="title" label="Title" name="name"
                                        placeholder="Enter the title of your event" validation="required|string" />

                                    <div class="w-full flex flex-col space-y-2">
                                        <label class="text-lg font-semibold">Description</label>
                                        <RichTextEditor theme="snow" class="editor" required
                                            v-model:content="description" contentType="html" :editor="ClassicEditor"
                                            v-model="description" :config="editorConfig"></RichTextEditor>

                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold mb-2">Start & End Date</h3>
                                        <datepicker @cleared="isCleared" required position="left"
                                            placeholder="select start & end date" :range="true"
                                            input-class-name="datepicker bg-red-500" format="dd/MM/yyyy HH:mm"
                                            v-model="dateRange" />
                                    </div>
                                    <div class="w-full flex flex-col space-y-2">
                                        <label class="text-lg font-semibold">Category</label>
                                        <div class="flex flex-wrap space-x-2">
                                            <button type="button" @click="selectedCategory = category"
                                                class="flex items-center bg-gray-100 font-light hover:bg-red-600 hover:text-white transition duration-150 rounded-full px-1.5 py-1.5 mb-1.5"
                                                v-for="category in $categories"
                                                :class="selectedCategory.name === category.name ? 'bg-red-600 text-white' : ''">
                                                <img :src="`${runtimeConfig.public.baseUrl}storage/categories/${category.icon}`"
                                                    class="w-6 h-6 mr-2" />
                                                {{ category.name }}
                                            </button>
                                        </div>
                                    </div>
                                    <div class="w-full flex flex-col items-start space-y-2">
                                        <FormKit type="radio" label="Location type" :options="locationType"
                                            v-model="selectedLocationType" validation="required" />
                                        <FormKit v-if="selectedLocationType === 'Online'" type="text"
                                            label="Meeting link" name="location" :prefixIcon="meetingIcon"
                                            v-model="meetingLink" placeholder="Enter or copy & paste meeting link"
                                            :classes="{
                                                input: 'w-full pl-10 text-blue-500 py-1.5 border border-gray-300 rounded-none focus:ring-0 focus:outline-none',
                                            }" />

                                        <div class="w-full" v-else-if="selectedLocationType == 'Venue'">
                                            <label class="text-lg font-semibold mb-2">Location Picker</label>
                                            <CoreLocationPicker @update:location="onUpdateLocation" />
                                            <p v-if="location?.address" class="bg-red-50 mt-2 px-4 py-2">
                                                {{ location?.address }}
                                            </p>
                                            <p v-else-if="location?.street" class="bg-red-50 mt-2 px-4 py-2">
                                                {{ location?.street }}, {{ location?.city }}, {{
                                                    location?.country
                                                }}
                                            </p>
                                            <p v-else class="bg-red-50 mt-2 px-4 py-2">
                                                {{ location }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="w-full flex flex-col space-y-2">
                                        <FormKit type="radio" label="Visibility" :options="visibility"
                                            v-model="selectedVisibility" validation="required" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-span-2 order-2 border">
                            <div class="w-full bg-gray-50 flex items-center justify-between border-b px-4 py-2">
                                <h3 class="text-xl font-semibold">Uploads</h3>
                                <button class="bg-gray-100 w-8 h-8 rounded-none flex items-center justify-center">
                                    <Icon icon="lucide:minimize-2" class="w-5 h-5" />
                                </button>
                            </div>
                            <div class="px-5 py-2 flex flex-col space-y-3">
                                <div>
                                    <h3 class="text-lg font-semibold mb-2">Cover Art</h3>
                                    <EventsImagePicker v-if="!loading" @files-selected="onCoverPicker"
                                        @file-removed="onFileRemoved"
                                        :file="`${runtimeConfig.public.baseUrl}storage/events/${coverArt}`" />
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
                <div class="hidden bottom-0 left-0 right-0 bg-white shadow border-t p-4">
                    <div class="flex justify-end space-x-3">
                        <CoreSubmitButton ref="submitButtonRef" color="primary" :loading="loading" />
                    </div>
                </div>
            </FormKit>
        </div>
        <div class="fixed bottom-0 left-0 right-0 bg-white shadow border-t p-4">
            <div class="flex justify-end space-x-3">
                <!-- <button @click="navigatePreviousStep" v-if="currentStep > 1" class="border px-4 py-2">
                        Previous
                    </button> -->
                <button @click="navigateNextStep" class="bg-red-600 text-white flex items-center px-4 py-2">
                    {{ currentStep === 1 ? "Create Event" : currentStep === 2 ? "Generate tickets & Continue" :
                        "Finish" }}
                    <Icon icon="fluent:arrow-next-16-regular" class="w-5 h-5 ml-2" />
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Category, EventItem, Location } from '@/types';
import { ClassicEditor, } from 'ckeditor5';
import dayjs from 'dayjs';

const route = useRoute();

definePageMeta({
    layout: "dashboard",
});
useHead({
    title: "Edit Event - EventaHub Malawi",
});

const runtimeConfig = useRuntimeConfig();
const currentStep = ref<number>(1);
const submitButtonRef = ref()
const steps = ref([
    { name: 'Enter Required Details', completed: false },
    { name: 'Generate Tickets', completed: false },
    { name: 'Add Sponsors', completed: false },
    { name: 'Publish', completed: false }
]);
const eventDetails = ref<EventItem>();
const title = ref<string>("");
const description = ref<string>("");
const selectedCategory = ref<Category>({ id: 0, name: "select Category", icon: "other.png" });
const locationType = ref<string[]>(["Online", "Venue"]);
const selectedLocationType = ref<string>("");
const visibility = ref<string[]>(["Public", "Private"]);
const selectedVisibility = ref<string>("");
const dateRange = ref<any>([]);
const { finish } = useLoadingIndicator();
const location = ref<Location | any>();
const coverArt = ref();
const meetingLink = ref<string>("");
const meetingIcon = ref<string>("globe");
const loading = ref<boolean>(false);
const event = ref<EventItem | null>(null);
const { $toast, $categories }: any = useNuxtApp();
const httpClient = useHttpClient();

const stepActions = {
    1: () => submitButtonRef.value?.$el.click(),
    default: () => currentStep.value++
};

const navigateNextStep = (): void => {
    const action = stepActions[currentStep.value as keyof typeof stepActions] || stepActions.default;
    action();
};

const fetchEvent = async (): Promise<void> => {
    loading.value = true;
    try {
        const response: any = await httpClient.get(`${ENDPOINTS.EVENTS.SLUG}/${route.params.slug}`);
        event.value = response.event;
        populateForm(response.event);
    } catch (error: any) {
        $toast.error(error.message || 'Failed to fetch event');
    } finally {
        loading.value = false;
    }
};

const populateForm = (eventData: EventItem): void => {
    eventDetails.value = eventData;
    title.value = eventData.title;
    description.value = eventData.description;
    selectedCategory.value = $categories.find((cat: Category) => cat.id === eventData.category_id) || selectedCategory.value;
    selectedLocationType.value = eventData.location == "Remote" ? "Online" : "Venue";
    location.value = eventData.location;
    selectedVisibility.value = eventData.type_id == 1 ? "Public" : "Private";
    coverArt.value = eventData.cover_art;
    dateRange.value = [
        dayjs(eventData.start).toDate(),
        dayjs(eventData.end).toDate()
    ];

    if (eventData.location === "Online") {
        meetingLink.value = eventData.meeting_link?.link || "";
    }
};

const isCleared = (event: any): void => {
    console.log(event);
};

const onUpdateLocation = (e: any): void => {
    location.value = e;
}



const onFileRemoved = () => {
    $toast.warn("Image removed, please upload a new one");
}

const onCoverPicker = (e: File[]) => {
    coverArt.value = e[0];
}


const onFormSubmit = async (): Promise<void> => {
    loading.value = true;
    const formData = new FormData();

    formData.append("title", title.value);
    formData.append("description", description.value);
    formData.append("category", String(selectedCategory?.value?.id));

    if (selectedLocationType.value === "Venue") {
        const newLocationString = `${location?.value?.street || ''}, ${location?.value?.city || ''}, ${location?.value?.country || ''}`.trim();

        if (newLocationString !== eventDetails.value?.location) {
            formData.append("location", newLocationString);
            formData.append("latitude", location?.value?.latlong?.lat?.toString() || eventDetails.value?.latitude?.toString() || '');
            formData.append("longitude", location?.value?.latlong?.lng?.toString() || eventDetails.value?.longitude?.toString() || '');
            formData.append("district", location?.value?.district || "");
        }
    } else {
        if (meetingLink.value !== eventDetails.value?.meeting_link?.link) {
            formData.append("meeting_link", meetingLink.value);
        }
    }

    const newVisibility = selectedVisibility.value === "Public" ? "1" : "0";
    formData.append("visibility", newVisibility);
    formData.append("type", newVisibility);

    const startDate = dayjs(dateRange.value[0]).format("YYYY-MM-DD HH:mm:ss");
    const endDate = dayjs(dateRange.value[1]).format("YYYY-MM-DD HH:mm:ss");
    formData.append("start_date", startDate);
    formData.append("end_date", endDate);
    formData.append("locationType", selectedLocationType.value);
    if (coverArt.value === eventDetails.value?.cover_art) {
        const url = `${runtimeConfig.public.baseUrl}storage/events/${coverArt.value}`;
        fetch(url)
            .then((response) => response.blob())
            .then((blob) => {
                const file = new File([blob], "cover_art.jpg", { type: blob.type });
                formData.append("cover_art", file);
            })
            .catch((error) => {
                console.error("Error fetching the image:", error);
            });
    } else {
        formData.append("cover_art", coverArt.value);
    }


    try {
        const response: any = await httpClient.post(`${ENDPOINTS.EVENTS.UPDATE}/${Number(eventDetails.value?.id)}`, formData);
        $toast.success(response.message || 'Event updated successfully');
        navigateTo("/dashboard/manage-events");
    } catch (error: any) {
        if (error) {
            const errors = error.errors;
            Object.keys(errors).forEach((key: string) => {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach((message: string) => {
                        $toast.error(message);
                    });
                } else if (typeof errors[key] === 'string') {
                    $toast.error(errors[key]);
                }
            });
        } else if (error.message) {
            $toast.error(error.message);
        } else {
            $toast.error('Something went wrong, please try again later');
        }
        finish();
    } finally {
        loading.value = false;
        finish();
    }
};

watch(meetingLink, (newMeetingLink) => {
    const url = newMeetingLink.toLowerCase();
    const iconMap = {
        'zoom.us': 'zoom',
        'teams.microsoft.com': 'teams',
        'meet.google.com': 'meet',
    };
    let icon = "globe";
    for (const [keyword, mappedIcon] of Object.entries(iconMap)) {
        if (url.includes(keyword)) {
            icon = mappedIcon;
        }
    }
    meetingIcon.value = icon;
});

onMounted(() => {
    fetchEvent();
});

</script>

<style lang="css" scoped></style>