<template>
    <div class="w-full shadow-sm">
        <Datatable
            v-model:items-selected="itemsSelected"
            :headers="headers"
            :items="props.events"
            :events="props.events"
            :rows-items="[10, 25, 50, 100]"
            :server-options="serverOptions"
            :total-items="props.totalItems"
            @update:options="handleOptionsUpdate"
            alternating
            :loading="loading"
            theme-color="#dc2626"
            :search-field="searchField"
            :search-value="searchValue"
            server-side
        >
            <template #loading>
                <CoreLoader/>
            </template>
            <template #item-description="item">
                <div class="line-clamp-1" v-html="item.description"></div>
            </template>
            <template #item-actions="item">
                <div class="w-full flex items-center space-x-2">
                    <EventsDashboardView :event="item"/>
                    <button @click="navigateTo(`/dashboard/manage-events/edit/${item.slug}`)"
                        class="px-2 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded transition-colors">
                        <Icon icon="line-md:edit" class="w-5 h-5" />
                    </button>
                    <EventsDeleteDialog :event="item" @onEventDeleted="onEventDeleted"/>
                </div>
            </template>
        </Datatable>
    </div>
</template>

<script setup lang="ts">
const emits = defineEmits(['update:serverOptions', 'update:options', 'update:events'])
const props = defineProps({
    headers: {
        type: Array,
        required: true,
    },
    events: {
        type: Array,
        required: true,
    },
    totalItems: {
        type: Number,
        required: true,
    },
    serverOptions: {
        required: false,
        type: Object,
        default: () => ({
            page: 1,
            rowsPerPage: 25,
        })
    },
    loading: {
        required: true,
        type: Boolean,
        default: false
    }
})

const itemsSelected = ref([])
const searchField = ref('')
const searchValue = ref('')

const serverOptions = ref<Record<string, any>>({
    page: 1,
    rowsPerPage: 25,
});

const handleOptionsUpdate = (n: any) => {
    serverOptions.value = n
    emits('update:options', n)
}

const onEventDeleted = (value: boolean) => {
    if(value){
        emits('update:events', value)
    }
}

watch(() => props.serverOptions, (n) => {
    if (n) {
        serverOptions.value = n
        emits('update:serverOptions', n)
    }
}, { immediate: true })
</script>

<style scoped>
</style>
