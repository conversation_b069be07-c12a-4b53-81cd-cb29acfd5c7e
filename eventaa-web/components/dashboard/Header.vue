<template>
    <header class="bg-white flex items-center justify-between px-4 py-3 border-b dark:border-gray-700 dark:bg-black">
        <div class="flex items-center space-x-4">

            <button class="focus:outline-none lg:hidden text-gray-700 dark:text-gray-200" @click="toggleSidebar">
                <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 6H20M4 12H20M4 18H11" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </button>

            <button class="focus:outline-none hidden lg:block text-gray-700 dark:text-gray-200" @click="toggleMiniVariant">
                <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 6H20M4 12H20M4 18H11" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round" />
                </svg>
            </button>

            <div class="relative mx-4 lg:mx-0">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg class="w-5 h-5 text-gray-400" viewBox="0 0 24 24" fill="none">
                        <path
                            d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </span>

                <input class="w-32 bg-gray-100 dark:bg-zinc-800 text-gray-500 dark:text-gray-300 pl-10 py-1.5 pr-4 border-gray-200 dark:border-gray-600 rounded-md sm:w-64 focus:outline-none focus:ring-0"
                    type="text" placeholder="Search">
            </div>
        </div>

        <div class="flex items-center space-x-4">
            <div class="ml-4 flow-root lg:ml-6">
                <button class="group -m-2 flex items-center p-2" @click="toggleColorMode">
                    <Icon v-if="colorMode.value === 'dark'" icon="line-md:sunny-filled-loop"
                        class="h-6 w-6 flex-shrink-0 text-yellow-400 group-hover:text-yellow-300" aria-hidden="true" />
                    <Icon v-else icon="line-md:moon-filled"
                        class="h-6 w-6 flex-shrink-0 text-gray-500 group-hover:text-gray-600" aria-hidden="true" />
                </button>
            </div>

            <UserNotificationDrawer />

            <div class="relative" v-if="isAuthenticated">
                <div class="flex items-center">
                    <div class="hidden md:block mr-3 text-right">
                        <div class="text-sm font-medium text-gray-700 dark:text-gray-200">{{ user?.name }}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ userRoleDisplay }}</div>
                    </div>
                    <button class="relative z-10 block w-8 h-8 overflow-hidden rounded-full shadow focus:outline-none" @click="dropdownOpen = !dropdownOpen">
                        <img class="object-cover w-full h-full"
                            :src="user?.avatar ? `${runtimeConfig.public.baseUrl}storage/avatars/${user.avatar}` : `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.name || 'User')}`"
                            :alt="user?.name">
                    </button>
                </div>

                <div v-show="dropdownOpen" class="fixed inset-0 z-10 w-full h-full" @click="dropdownOpen = false" />

                <transition enter-active-class="transition duration-150 ease-out transform"
                    enter-from-class="scale-95 opacity-0" enter-to-class="scale-100 opacity-100"
                    leave-active-class="transition duration-150 ease-in transform"
                    leave-from-class="scale-100 opacity-100" leave-to-class="scale-95 opacity-0">
                    <div v-show="dropdownOpen"
                        class="absolute right-0 z-20 w-48 py-2 mt-2 bg-white dark:bg-gray-800 shadow-xl rounded-md">
                        <div class="px-4 py-2 border-b border-gray-100 dark:border-gray-700">
                            <div class="text-sm font-medium text-gray-700 dark:text-gray-200">{{ user?.name }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ user?.email }}</div>
                        </div>
                        <nuxt-link to="/dashboard/profile"
                            class="block px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-red-600 hover:text-white">
                            <div class="flex items-center">
                                <Icon icon="heroicons:user" class="w-4 h-4 mr-2" />
                                Profile
                            </div>
                        </nuxt-link>
                        <nuxt-link to="/dashboard/settings"
                            class="block px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-red-600 hover:text-white">
                            <div class="flex items-center">
                                <Icon icon="heroicons:cog-6-tooth" class="w-4 h-4 mr-2" />
                                Settings
                            </div>
                        </nuxt-link>
                        <button @click="logout"
                            class="w-full text-left block px-4 py-2 text-gray-700 dark:text-gray-200 hover:bg-red-600 hover:text-white">
                            <div class="flex items-center">
                                <Icon icon="heroicons:arrow-right-on-rectangle" class="w-4 h-4 mr-2" />
                                Log out
                            </div>
                        </button>
                    </div>
                </transition>
            </div>
        </div>
    </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '@/store/auth';

const dropdownOpen = ref(false);
const runtimeConfig = useRuntimeConfig();
const { isOpen, isMiniVariant } = useSidebar();
const authStore = useAuthStore();
const { user, isAuthenticated } = authStore;
const colorMode = useColorMode();

// User role information
const userRoles = computed(() => {
  return authStore.user?.roles || [];
});

const hasAdminRole = computed(() => {
  return userRoles.value.includes('admin');
});

const hasHostRole = computed(() => {
  return userRoles.value.includes('host');
});

// Role-specific display name
const userRoleDisplay = computed(() => {
  if (hasAdminRole.value) return 'Administrator';
  if (hasHostRole.value) return 'Event Host';
  return 'User';
});

const toggleSidebar = () => {
    isOpen.value = !isOpen.value;
}

const toggleMiniVariant = () => {
    isMiniVariant.value = !isMiniVariant.value;
}

const toggleColorMode = () => {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark';
}

const logout = async () => {
  try {
    // In a real implementation, this would call an API endpoint
    // await httpClient.post(ENDPOINTS.AUTH.LOGOUT);
    authStore.clearAuth();
    navigateTo('/', { replace: true });
  } catch (error) {
    console.error('Logout error:', error);
  }
}
</script>