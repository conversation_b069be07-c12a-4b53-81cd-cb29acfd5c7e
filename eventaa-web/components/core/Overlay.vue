<template>
    <transition name="fade">
        <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            >
            <div class="p-6">
                <div class="flex flex-col items-center space-y-4">
                    <CoreLoader color="white" height="100" width="100" />
                    <p v-if="text" class="text-gray-50 font-medium">{{ text }}</p>
                    <slot></slot>
                </div>
            </div>
        </div>
    </transition>
</template>

<script setup>
defineProps({
    loading: {
        type: Boolean,
        required: true
    },
    text: {
        type: String,
        default: 'Loading...'
    },
    dismissible: {
        type: Boolean,
        default: false
    }
})

defineEmits(['update:loading'])
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>